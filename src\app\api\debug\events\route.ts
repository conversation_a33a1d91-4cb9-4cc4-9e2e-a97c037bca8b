import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";

// GET /api/debug/events - Debug endpoint to check event data
export async function GET(request: NextRequest) {
    try {
        const supabase = await createClient();
        const { searchParams } = new URL(request.url);
        const slug = searchParams.get('slug');

        if (slug) {
            // Check specific event by slug
            const { data: event, error } = await supabase
                .from('events')
                .select('*')
                .eq('slug', slug)
                .single();

            return NextResponse.json({
                success: true,
                slug,
                event,
                error,
                message: `Debug info for event with slug: ${slug}`
            });
        } else {
            // Get all events with basic info
            const { data: events, error } = await supabase
                .from('events')
                .select('id, title, slug, is_active, published_at, is_featured')
                .order('created_at', { ascending: false });

            return NextResponse.json({
                success: true,
                events,
                error,
                count: events?.length || 0,
                message: 'All events basic info'
            });
        }
    } catch (error) {
        console.error('Debug API Error:', error);
        return NextResponse.json(
            { 
                success: false, 
                error: error instanceof Error ? error.message : 'Unknown error',
                message: 'Debug API failed'
            },
            { status: 500 }
        );
    }
}
